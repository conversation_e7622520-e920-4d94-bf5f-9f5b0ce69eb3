import React, { useState } from 'react';
import { 
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { isValidPhone, cn } from '../lib/utils';

const ProfilePage = () => {
  const { user, updateProfile, logout } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    full_name: user?.full_name || '',
    phone: user?.phone || ''
  });
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = '<PERSON>a lengkap wajib diisi';
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = '<PERSON>a lengkap minimal 2 karakter';
    } else if (formData.full_name.trim().length > 100) {
      newErrors.full_name = 'Nama lengkap maksimal 100 karakter';
    }

    if (formData.phone && !isValidPhone(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid (contoh: 081234567890)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const result = await updateProfile(formData);
      
      if (result.success) {
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Profile update error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      full_name: user?.full_name || '',
      phone: user?.phone || ''
    });
    setErrors({});
    setIsEditing(false);
  };

  const handleLogout = async () => {
    if (window.confirm('Apakah Anda yakin ingin keluar?')) {
      await logout();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Profil Saya</h1>
            <p className="text-gray-600">Kelola informasi profil Anda</p>
          </div>

          {/* Profile Card */}
          <div className="card mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Informasi Pribadi</h2>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="btn btn-outline btn-sm flex items-center space-x-2"
                >
                  <PencilIcon className="w-4 h-4" />
                  <span>Edit</span>
                </button>
              )}
            </div>

            {isEditing ? (
              /* Edit Form */
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Full Name */}
                <div>
                  <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Lengkap *
                  </label>
                  <input
                    id="full_name"
                    name="full_name"
                    type="text"
                    required
                    value={formData.full_name}
                    onChange={handleChange}
                    className={cn(
                      "input w-full",
                      errors.full_name && "border-error-300 focus:border-error-500 focus:ring-error-500"
                    )}
                    placeholder="Masukkan nama lengkap Anda"
                  />
                  {errors.full_name && (
                    <p className="mt-1 text-sm text-error-600">{errors.full_name}</p>
                  )}
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Nomor Telepon
                  </label>
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    className={cn(
                      "input w-full",
                      errors.phone && "border-error-300 focus:border-error-500 focus:ring-error-500"
                    )}
                    placeholder="081234567890"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-error-600">{errors.phone}</p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn btn-primary flex items-center space-x-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner w-4 h-4"></div>
                        <span>Menyimpan...</span>
                      </>
                    ) : (
                      <>
                        <CheckIcon className="w-4 h-4" />
                        <span>Simpan</span>
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="btn btn-outline flex items-center space-x-2"
                  >
                    <XMarkIcon className="w-4 h-4" />
                    <span>Batal</span>
                  </button>
                </div>
              </form>
            ) : (
              /* Display Mode */
              <div className="space-y-6">
                {/* Profile Picture */}
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                    <UserIcon className="w-8 h-8 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{user?.full_name}</h3>
                    <p className="text-sm text-gray-600 capitalize">{user?.role}</p>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <EnvelopeIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-900">{user?.email}</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nomor Telepon
                    </label>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <PhoneIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-900">
                        {user?.phone || 'Belum diisi'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Account Info */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Informasi Akun
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600">Status Email</span>
                      <span className={cn(
                        "text-sm font-medium",
                        user?.email_verified ? "text-success-600" : "text-warning-600"
                      )}>
                        {user?.email_verified ? 'Terverifikasi' : 'Belum Terverifikasi'}
                      </span>
                    </div>
                    <div className="flex justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600">Bergabung</span>
                      <span className="text-gray-900">
                        {user?.created_at ? new Date(user.created_at).toLocaleDateString('id-ID') : '-'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Account Actions */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Pengaturan Akun</h2>
            
            <div className="space-y-4">
              {/* Change Password */}
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h3 className="font-medium text-gray-900">Ubah Password</h3>
                  <p className="text-sm text-gray-600">Perbarui password untuk keamanan akun</p>
                </div>
                <button className="btn btn-outline btn-sm">
                  Ubah Password
                </button>
              </div>

              {/* Email Verification */}
              {!user?.email_verified && (
                <div className="flex items-center justify-between p-4 border border-warning-200 bg-warning-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-warning-900">Verifikasi Email</h3>
                    <p className="text-sm text-warning-700">Verifikasi email Anda untuk keamanan tambahan</p>
                  </div>
                  <button className="btn btn-warning btn-sm">
                    Kirim Verifikasi
                  </button>
                </div>
              )}

              {/* Logout */}
              <div className="flex items-center justify-between p-4 border border-error-200 rounded-lg">
                <div>
                  <h3 className="font-medium text-error-900">Keluar dari Akun</h3>
                  <p className="text-sm text-error-700">Keluar dari akun Anda di perangkat ini</p>
                </div>
                <button 
                  onClick={handleLogout}
                  className="btn btn-error btn-sm"
                >
                  Keluar
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
