import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  MapPinIcon,
  HomeIcon,
  StarIcon,
  HeartIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { kostAPI, favoritesAPI, handleAPIError } from '../lib/api';
import { formatCurrency, debounce, cn } from '../lib/utils';
import { useAuth } from '../contexts/AuthContext';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [properties, setProperties] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [showFilters, setShowFilters] = useState(false);
  const { isAuthenticated } = useAuth();

  // Search filters state
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    city: searchParams.get('city') || '',
    price_min: searchParams.get('price_min') || '',
    price_max: searchParams.get('price_max') || '',
    gender_type: searchParams.get('gender_type') || 'all',
    property_type: searchParams.get('property_type') || 'all',
    page: parseInt(searchParams.get('page')) || 1,
    limit: 12,
    sort_by: searchParams.get('sort_by') || 'created_at',
    sort_order: searchParams.get('sort_order') || 'DESC'
  });

  // Debounced search function
  const debouncedSearch = debounce((newFilters) => {
    fetchProperties(newFilters);
  }, 500);

  // Fetch properties
  const fetchProperties = async (searchFilters = filters) => {
    try {
      setIsLoading(true);
      const response = await kostAPI.getProperties(searchFilters);

      if (response.success) {
        setProperties(response.data.properties);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Update URL params
  const updateURLParams = (newFilters) => {
    const params = new URLSearchParams();
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        params.set(key, value);
      }
    });

    setSearchParams(params);
  };

  // Handle filter change
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    updateURLParams(newFilters);
    
    if (key === 'search') {
      debouncedSearch(newFilters);
    } else {
      fetchProperties(newFilters);
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    updateURLParams(newFilters);
    fetchProperties(newFilters);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Toggle favorite
  const toggleFavorite = async (propertyId) => {
    if (!isAuthenticated) return;

    try {
      const property = properties.find(p => p.id === propertyId);
      
      if (property.is_favorited) {
        await favoritesAPI.removeFavorite(propertyId);
      } else {
        await favoritesAPI.addFavorite(propertyId);
      }

      // Update local state
      setProperties(prev => prev.map(p => 
        p.id === propertyId 
          ? { ...p, is_favorited: !p.is_favorited }
          : p
      ));
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Initial load
  useEffect(() => {
    fetchProperties();
  }, []);

  const genderOptions = [
    { value: 'all', label: 'Semua' },
    { value: 'male', label: 'Pria' },
    { value: 'female', label: 'Wanita' },
    { value: 'mixed', label: 'Campur' }
  ];

  const propertyTypeOptions = [
    { value: 'all', label: 'Semua' },
    { value: 'kost', label: 'Kost' },
    { value: 'boarding_house', label: 'Rumah Kost' },
    { value: 'apartment', label: 'Apartemen' }
  ];

  const sortOptions = [
    { value: 'created_at-DESC', label: 'Terbaru' },
    { value: 'price_min-ASC', label: 'Harga Terendah' },
    { value: 'price_min-DESC', label: 'Harga Tertinggi' },
    { value: 'view_count-DESC', label: 'Terpopuler' },
    { value: 'name-ASC', label: 'Nama A-Z' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Search Header */}
        <div className="bg-white rounded-lg shadow-soft p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Cari berdasarkan kota, lokasi, atau nama kost..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="input pl-10 w-full"
              />
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn btn-outline flex items-center space-x-2"
            >
              <FunnelIcon className="w-4 h-4" />
              <span>Filter</span>
            </button>

            {/* Sort */}
            <select
              value={`${filters.sort_by}-${filters.sort_order}`}
              onChange={(e) => {
                const [sort_by, sort_order] = e.target.value.split('-');
                handleFilterChange('sort_by', sort_by);
                handleFilterChange('sort_order', sort_order);
              }}
              className="input min-w-[150px]"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* City Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kota
                  </label>
                  <input
                    type="text"
                    placeholder="Masukkan nama kota"
                    value={filters.city}
                    onChange={(e) => handleFilterChange('city', e.target.value)}
                    className="input w-full"
                  />
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Harga Minimum
                  </label>
                  <input
                    type="number"
                    placeholder="0"
                    value={filters.price_min}
                    onChange={(e) => handleFilterChange('price_min', e.target.value)}
                    className="input w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Harga Maksimum
                  </label>
                  <input
                    type="number"
                    placeholder="10000000"
                    value={filters.price_max}
                    onChange={(e) => handleFilterChange('price_max', e.target.value)}
                    className="input w-full"
                  />
                </div>

                {/* Gender Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipe Gender
                  </label>
                  <select
                    value={filters.gender_type}
                    onChange={(e) => handleFilterChange('gender_type', e.target.value)}
                    className="input w-full"
                  >
                    {genderOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Clear Filters */}
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    const resetFilters = {
                      search: '',
                      city: '',
                      price_min: '',
                      price_max: '',
                      gender_type: 'all',
                      property_type: 'all',
                      page: 1,
                      limit: 12,
                      sort_by: 'created_at',
                      sort_order: 'DESC'
                    };
                    setFilters(resetFilters);
                    setSearchParams(new URLSearchParams());
                    fetchProperties(resetFilters);
                  }}
                  className="btn btn-ghost btn-sm"
                >
                  Reset Filter
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {filters.search ? `Hasil pencarian "${filters.search}"` : 'Semua Kost'}
                </h1>
                {!isLoading && (
                  <p className="text-gray-600 mt-1">
                    Ditemukan {pagination.total_items || 0} kost
                  </p>
                )}
              </div>
            </div>

            {/* Properties Grid */}
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="card animate-pulse">
                    <div className="aspect-video bg-gray-300 rounded-lg mb-4"></div>
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                      <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : properties.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {properties.map((property) => (
                    <div key={property.id} className="card group hover:shadow-medium transition-all duration-300">
                      <div className="relative">
                        <Link to={`/property/${property.id}`}>
                          <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                            {property.main_photo_url ? (
                              <img
                                src={property.main_photo_url}
                                alt={property.name}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <HomeIcon className="w-12 h-12 text-gray-400" />
                              </div>
                            )}
                          </div>
                        </Link>

                        {/* Favorite Button */}
                        {isAuthenticated && (
                          <button
                            onClick={() => toggleFavorite(property.id)}
                            className="absolute top-3 right-3 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors"
                          >
                            {property.is_favorited ? (
                              <HeartSolidIcon className="w-5 h-5 text-red-500" />
                            ) : (
                              <HeartIcon className="w-5 h-5 text-gray-600" />
                            )}
                          </button>
                        )}

                        {/* Featured Badge */}
                        {property.is_featured && (
                          <div className="absolute top-3 left-3 bg-primary-600 text-white px-2 py-1 rounded-md text-xs font-medium">
                            Pilihan
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-3">
                        <Link to={`/property/${property.id}`}>
                          <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                            {property.name}
                          </h3>
                        </Link>
                        
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPinIcon className="w-4 h-4 mr-1" />
                          {property.city}, {property.province}
                        </div>

                        {/* Facilities */}
                        {property.facilities && property.facilities.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {property.facilities.slice(0, 3).map((facility, index) => (
                              <span
                                key={index}
                                className="badge badge-secondary text-xs"
                              >
                                {facility.name}
                              </span>
                            ))}
                            {property.facilities.length > 3 && (
                              <span className="badge badge-secondary text-xs">
                                +{property.facilities.length - 3}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="text-lg font-bold text-primary-600">
                            {formatCurrency(property.price_min)}
                            {property.price_min !== property.price_max && (
                              <span className="text-sm font-normal text-gray-500">
                                - {formatCurrency(property.price_max)}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center text-sm text-gray-600">
                            <StarIcon className="w-4 h-4 text-yellow-400 mr-1" />
                            4.5
                          </div>
                        </div>

                        {/* Availability */}
                        <div className="text-sm text-gray-600">
                          {property.available_rooms} dari {property.total_rooms} kamar tersedia
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={!pagination.has_prev}
                        className="btn btn-outline btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Sebelumnya
                      </button>

                      {[...Array(Math.min(5, pagination.total_pages))].map((_, index) => {
                        const page = index + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={cn(
                              "btn btn-sm",
                              page === pagination.current_page
                                ? "btn-primary"
                                : "btn-outline"
                            )}
                          >
                            {page}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={!pagination.has_next}
                        className="btn btn-outline btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Selanjutnya
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <HomeIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Tidak ada kost ditemukan
                </h3>
                <p className="text-gray-600 mb-4">
                  Coba ubah filter pencarian atau kata kunci Anda
                </p>
                <button
                  onClick={() => {
                    const resetFilters = {
                      search: '',
                      city: '',
                      price_min: '',
                      price_max: '',
                      gender_type: 'all',
                      property_type: 'all',
                      page: 1,
                      limit: 12,
                      sort_by: 'created_at',
                      sort_order: 'DESC'
                    };
                    setFilters(resetFilters);
                    setSearchParams(new URLSearchParams());
                    fetchProperties(resetFilters);
                  }}
                  className="btn btn-primary"
                >
                  Reset Pencarian
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
