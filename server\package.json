{"name": "server", "version": "1.0.0", "description": "Backend API for Kost Platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["kost", "api", "express", "postgresql"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@types/node": "^24.0.15", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.19.2", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "postcss": "^8.5.6", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "uuid": "^11.0.4", "validator": "^13.12.0"}, "devDependencies": {"nodemon": "^3.1.10"}}