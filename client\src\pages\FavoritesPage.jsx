import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  HeartIcon,
  MapPinIcon,
  HomeIcon,
  StarIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { favoritesAPI, handleAPIError } from '../lib/api';
import { formatCurrency, cn } from '../lib/utils';

const FavoritesPage = () => {
  const [favorites, setFavorites] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);

  const fetchFavorites = async (page = 1) => {
    try {
      setIsLoading(true);
      const response = await favoritesAPI.getFavorites({ page, limit: 12 });
      
      if (response.success) {
        setFavorites(response.data.properties);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const removeFavorite = async (propertyId) => {
    try {
      await favoritesAPI.removeFavorite(propertyId);
      
      // Remove from local state
      setFavorites(prev => prev.filter(property => property.id !== propertyId));
      
      // Update pagination if needed
      if (favorites.length === 1 && currentPage > 1) {
        setCurrentPage(prev => prev - 1);
        fetchFavorites(currentPage - 1);
      }
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchFavorites(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  useEffect(() => {
    fetchFavorites();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Kost Favorit</h1>
            <p className="text-gray-600">Kost-kost yang telah Anda simpan</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="card animate-pulse">
                <div className="aspect-video bg-gray-300 rounded-lg mb-4"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Kost Favorit</h1>
          <p className="text-gray-600">
            {favorites.length > 0 
              ? `${pagination.total_items || favorites.length} kost yang telah Anda simpan`
              : 'Belum ada kost yang disimpan'
            }
          </p>
        </div>

        {favorites.length > 0 ? (
          <>
            {/* Favorites Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {favorites.map((property) => (
                <div key={property.id} className="card group hover:shadow-medium transition-all duration-300">
                  <div className="relative">
                    <Link to={`/property/${property.id}`}>
                      <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                        {property.main_photo_url ? (
                          <img
                            src={property.main_photo_url}
                            alt={property.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <HomeIcon className="w-12 h-12 text-gray-400" />
                          </div>
                        )}
                      </div>
                    </Link>

                    {/* Remove Favorite Button */}
                    <button
                      onClick={() => removeFavorite(property.id)}
                      className="absolute top-3 right-3 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors group/btn"
                      title="Hapus dari favorit"
                    >
                      <HeartSolidIcon className="w-5 h-5 text-red-500 group-hover/btn:hidden" />
                      <TrashIcon className="w-4 h-4 text-red-600 hidden group-hover/btn:block" />
                    </button>

                    {/* Featured Badge */}
                    {property.is_featured && (
                      <div className="absolute top-3 left-3 bg-primary-600 text-white px-2 py-1 rounded-md text-xs font-medium">
                        Pilihan
                      </div>
                    )}

                    {/* Favorited Date */}
                    <div className="absolute bottom-3 left-3 bg-black/70 text-white px-2 py-1 rounded-md text-xs">
                      Disimpan {new Date(property.favorited_at).toLocaleDateString('id-ID')}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <Link to={`/property/${property.id}`}>
                      <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                        {property.name}
                      </h3>
                    </Link>
                    
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPinIcon className="w-4 h-4 mr-1" />
                      {property.city}, {property.province}
                    </div>

                    {/* Facilities */}
                    {property.facilities && property.facilities.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {property.facilities.slice(0, 3).map((facility, index) => (
                          <span
                            key={index}
                            className="badge badge-secondary text-xs"
                          >
                            {facility.name}
                          </span>
                        ))}
                        {property.facilities.length > 3 && (
                          <span className="badge badge-secondary text-xs">
                            +{property.facilities.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="text-lg font-bold text-primary-600">
                        {formatCurrency(property.price_min)}
                        {property.price_min !== property.price_max && (
                          <span className="text-sm font-normal text-gray-500">
                            - {formatCurrency(property.price_max)}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <StarIcon className="w-4 h-4 text-yellow-400 mr-1" />
                        4.5
                      </div>
                    </div>

                    {/* Availability */}
                    <div className="text-sm text-gray-600">
                      {property.available_rooms} dari {property.total_rooms} kamar tersedia
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2 pt-2">
                      <Link
                        to={`/property/${property.id}`}
                        className="btn btn-primary btn-sm flex-1"
                      >
                        Lihat Detail
                      </Link>
                      <button
                        onClick={() => removeFavorite(property.id)}
                        className="btn btn-outline btn-sm px-3"
                        title="Hapus dari favorit"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.total_pages > 1 && (
              <div className="flex justify-center">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={!pagination.has_prev}
                    className="btn btn-outline btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Sebelumnya
                  </button>

                  {[...Array(Math.min(5, pagination.total_pages))].map((_, index) => {
                    const page = index + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={cn(
                          "btn btn-sm",
                          page === pagination.current_page
                            ? "btn-primary"
                            : "btn-outline"
                        )}
                      >
                        {page}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={!pagination.has_next}
                    className="btn btn-outline btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Selanjutnya
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="text-center py-16">
            <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Belum Ada Kost Favorit
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Mulai jelajahi kost-kost menarik dan simpan yang Anda sukai untuk melihatnya nanti
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/search" className="btn btn-primary">
                Jelajahi Kost
              </Link>
              <Link to="/" className="btn btn-outline">
                Kembali ke Beranda
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
