import React from 'react';
import { Link } from 'react-router-dom';
import { HomeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center px-4">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-primary-600 mb-4">404</div>
          <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <HomeIcon className="w-12 h-12 text-primary-600" />
          </div>
        </div>

        {/* Content */}
        <div className="max-w-md mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Halaman Tidak Ditemukan
          </h1>
          <p className="text-gray-600 mb-8 leading-relaxed">
            Maaf, halaman yang Anda cari tidak dapat ditemukan. 
            Mungkin halaman telah dipindahkan atau URL yang Anda masukkan salah.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/" 
              className="btn btn-primary flex items-center justify-center space-x-2"
            >
              <HomeIcon className="w-4 h-4" />
              <span>Kembali ke Beranda</span>
            </Link>
            
            <Link 
              to="/search" 
              className="btn btn-outline flex items-center justify-center space-x-2"
            >
              <MagnifyingGlassIcon className="w-4 h-4" />
              <span>Cari Kost</span>
            </Link>
          </div>

          {/* Help Text */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Butuh bantuan? Hubungi kami di{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary-600 hover:text-primary-500"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
