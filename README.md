# KostKu - Platform Pencarian Kost

Platform online untuk pencarian kost dengan tiga role utama: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan User <PERSON><PERSON><PERSON>.

## 🚀 Status Implementasi

### ✅ Completed Features
1. **Database Setup and Schema Design** - Complete
   - PostgreSQL schema dengan 10+ tables
   - Relationships dan indexes yang optimal
   - Sample data untuk testing

2. **Backend API Foundation** - Complete
   - Express.js server dengan middleware lengkap
   - Authentication dengan JWT
   - Rate limiting dan security headers
   - API routes untuk auth, kost, dan favorites

3. **Frontend Foundation Setup** - Complete
   - React dengan Vite
   - Tailwind CSS untuk styling
   - React Router untuk navigation
   - Context API untuk state management

### 🔄 Currently Working On
4. **Public Search Feature (No Login)** - In Progress
   - Basic search functionality ✅
   - Property listing ✅
   - Filtering dan sorting ✅
   - Limited property details untuk non-authenticated users ✅

### 📋 Next Features
5. **User Authentication System**
6. **Detailed View for Authenticated Users**
7. **User Features (Favorites & Comparison)**
8. **Testing and Bug Fixes**

## 🏗️ Architecture

```
kost/
├── client/          # React Frontend (Port 5174)
├── server/          # Express.js Backend (Port 5000)
├── database/        # PostgreSQL Schema & Seeds
└── requirements.txt # MVP Specifications
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js (v18+)
- PostgreSQL (v12+)
- Git

### 1. Database Setup

```bash
# Install PostgreSQL dan buat database
createdb kost

# Jalankan schema dan seed data
psql -d kost -f database/schema.sql
psql -d kost -f database/seed_data.sql
```

### 2. Backend Setup

```bash
cd server
npm install
cp .env.example .env  # Edit database credentials
npm run dev           # Starts on port 5000
```

### 3. Frontend Setup

```bash
cd client
npm install
npm run dev           # Starts on port 5174
```

## 🎯 MVP Features

### 1. User Pencari Kost (Tanpa Login) ✅
- ✅ Mencari kost berdasarkan lokasi, harga, tipe kamar, dan fasilitas
- ✅ Melihat daftar hasil pencarian (foto utama, lokasi umum, harga)
- ✅ Melihat ringkasan informasi kost secara umum (terbatas)

### 2. User Pencari Kost (Login Required) 🔄
- ✅ Melihat detail lengkap kost (fasilitas, galeri foto, kontak pemilik)
- ✅ Fitur favorit dan membandingkan kost
- 🔄 Authentication system

### 3. Pemilik Kost 📋
- 📋 Registrasi dan manajemen profil pemilik
- 📋 Input, edit, dan hapus informasi kost
- 📋 Upload foto-foto kost
- 📋 Mengatur status kamar
- 📋 Statistik pengunjung

### 4. Admin 📋
- 📋 Mengelola user (aktivasi, suspend, delete)
- 📋 Mengelola data kost
- 📋 Monitoring aktivitas platform
- 📋 Dashboard statistik

## 🔧 Tech Stack

### Frontend
- **React 19** - UI Framework
- **Vite** - Build tool
- **Tailwind CSS** - Styling
- **React Router** - Navigation
- **Axios** - HTTP client
- **Heroicons** - Icons

### Backend
- **Express.js 4** - Web framework
- **PostgreSQL** - Database
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **Helmet** - Security headers
- **CORS** - Cross-origin requests

### Database
- **PostgreSQL** - Primary database
- **UUID** - Primary keys
- **Indexes** - Performance optimization

## 📱 UI/UX Features

- ✅ Responsive design (mobile-first)
- ✅ Dark/light mode support
- ✅ Smooth animations dan transitions
- ✅ Loading states dan error handling
- ✅ Accessible components (ARIA)
- ✅ SEO-friendly structure

## 🔐 Security Features

- ✅ JWT-based authentication
- ✅ Password hashing dengan bcrypt
- ✅ Rate limiting
- ✅ CORS protection
- ✅ Helmet security headers
- ✅ Input validation
- ✅ SQL injection prevention

## 📊 Database Schema

### Core Tables
- `users` - User authentication dan profiles
- `kost_properties` - Main kost information
- `rooms` - Individual rooms
- `facilities` - Master facilities list
- `property_facilities` - Property-level facilities
- `room_facilities` - Room-level facilities
- `photos` - Images untuk properties dan rooms
- `favorites` - User favorites
- `testimonials` - Reviews dan ratings
- `property_views` - Analytics tracking

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kost
   ```

2. **Setup Database**
   - Install PostgreSQL
   - Create database "kost" dengan password "vicky"
   - Run schema.sql dan seed_data.sql

3. **Start Backend**
   ```bash
   cd server
   npm install
   npm run dev
   ```

4. **Start Frontend**
   ```bash
   cd client
   npm install
   npm run dev
   ```

5. **Access Application**
   - Frontend: http://localhost:5174
   - Backend API: http://localhost:5000
   - Health Check: http://localhost:5000/health

## 🧪 Testing

### Manual Testing Checklist
- [ ] Homepage loads correctly
- [ ] Search functionality works
- [ ] Property listing displays
- [ ] Property detail page works
- [ ] User registration/login
- [ ] Favorites functionality
- [ ] Responsive design
- [ ] Error handling

### API Testing
```bash
# Health check
curl http://localhost:5000/health

# Get properties
curl http://localhost:5000/api/kost

# Search properties
curl "http://localhost:5000/api/kost?city=Malang&price_max=1000000"
```

## 📝 Development Notes

### Current Implementation Status
- ✅ Database schema dengan sample data
- ✅ Backend API dengan authentication
- ✅ Frontend dengan routing dan state management
- ✅ Public search tanpa login
- 🔄 User authentication flow
- 📋 Owner dashboard
- 📋 Admin panel

### Known Issues
- Database connection perlu PostgreSQL setup
- Image upload belum implemented
- Email verification belum active
- Social login belum implemented

### Next Steps
1. Complete user authentication system
2. Implement detailed property views untuk authenticated users
3. Add favorites dan comparison features
4. Create owner dashboard
5. Build admin panel
6. Add comprehensive testing
7. Deploy to production

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is licensed under the MIT License.

---

**KostKu** - Temukan kost impian Anda dengan mudah! 🏠✨
