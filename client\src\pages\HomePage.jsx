import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  MagnifyingGlassIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  HomeIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { kostAPI, handleAPIError } from '../lib/api';
import { formatCurrency, cn } from '../lib/utils';

const HomePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [featuredProperties, setFeaturedProperties] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Fetch featured properties
  useEffect(() => {
    const fetchFeaturedProperties = async () => {
      try {
        const response = await kostAPI.getProperties({
          limit: 6,
          sort_by: 'view_count',
          sort_order: 'DESC'
        });

        if (response.success) {
          setFeaturedProperties(response.data.properties);
        }
      } catch (error) {
        console.error('Error fetching featured properties:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedProperties();
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?search=${encodeURIComponent(searchQuery.trim())}`);
    } else {
      navigate('/search');
    }
  };

  const popularCities = [
    { name: 'Jakarta', count: '500+', image: '/images/jakarta.jpg' },
    { name: 'Bandung', count: '300+', image: '/images/bandung.jpg' },
    { name: 'Yogyakarta', count: '250+', image: '/images/yogya.jpg' },
    { name: 'Surabaya', count: '200+', image: '/images/surabaya.jpg' },
    { name: 'Malang', count: '150+', image: '/images/malang.jpg' },
    { name: 'Semarang', count: '100+', image: '/images/semarang.jpg' },
  ];

  const features = [
    {
      icon: MagnifyingGlassIcon,
      title: 'Pencarian Mudah',
      description: 'Temukan kost impian dengan filter lokasi, harga, dan fasilitas'
    },
    {
      icon: MapPinIcon,
      title: 'Lokasi Strategis',
      description: 'Kost-kost pilihan di lokasi strategis dekat kampus dan pusat kota'
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Harga Transparan',
      description: 'Tidak ada biaya tersembunyi, semua harga sudah termasuk fasilitas'
    },
    {
      icon: HomeIcon,
      title: 'Fasilitas Lengkap',
      description: 'Kost dengan fasilitas modern dan nyaman untuk kehidupan sehari-hari'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container py-20 lg:py-32">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Temukan <span className="text-gradient">Kost Impian</span> Anda
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 leading-relaxed">
              Platform terpercaya untuk mencari kost dengan fasilitas lengkap 
              dan harga terjangkau di seluruh Indonesia
            </p>

            {/* Search Form */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="flex flex-col sm:flex-row gap-4 p-2 bg-white rounded-2xl shadow-large">
                <div className="flex-1 relative">
                  <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Cari berdasarkan kota, lokasi, atau nama kost..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 text-gray-900 placeholder-gray-500 border-0 rounded-xl focus:ring-2 focus:ring-primary-500 focus:outline-none"
                  />
                </div>
                <button
                  type="submit"
                  className="btn btn-primary btn-lg px-8 whitespace-nowrap"
                >
                  Cari Kost
                </button>
              </div>
            </form>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-bold mb-1">1000+</div>
                <div className="text-primary-200 text-sm">Kost Tersedia</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-1">50+</div>
                <div className="text-primary-200 text-sm">Kota</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-1">5000+</div>
                <div className="text-primary-200 text-sm">Pengguna</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-1">4.8</div>
                <div className="text-primary-200 text-sm">Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Cities */}
      <section className="section bg-white">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Kota Populer
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Jelajahi kost-kost terbaik di kota-kota favorit mahasiswa dan pekerja
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {popularCities.map((city) => (
              <Link
                key={city.name}
                to={`/search?city=${encodeURIComponent(city.name)}`}
                className="group block"
              >
                <div className="card hover:shadow-medium transition-all duration-300 group-hover:-translate-y-1">
                  <div className="aspect-square bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg mb-4 flex items-center justify-center">
                    <MapPinIcon className="w-8 h-8 text-primary-600" />
                  </div>
                  <div className="text-center">
                    <h3 className="font-semibold text-gray-900 mb-1">{city.name}</h3>
                    <p className="text-sm text-gray-600">{city.count} kost</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="section bg-gray-50">
        <div className="container">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Kost Pilihan
              </h2>
              <p className="text-xl text-gray-600">
                Kost-kost terpopuler dengan rating terbaik
              </p>
            </div>
            <Link
              to="/search"
              className="btn btn-outline hidden md:flex items-center space-x-2"
            >
              <span>Lihat Semua</span>
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="card animate-pulse">
                  <div className="aspect-video bg-gray-300 rounded-lg mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredProperties.map((property) => (
                <Link
                  key={property.id}
                  to={`/property/${property.id}`}
                  className="group block"
                >
                  <div className="card hover:shadow-medium transition-all duration-300 group-hover:-translate-y-1">
                    <div className="aspect-video bg-gray-200 rounded-lg mb-4 overflow-hidden">
                      {property.main_photo_url ? (
                        <img
                          src={property.main_photo_url}
                          alt={property.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <HomeIcon className="w-12 h-12 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                        {property.name}
                      </h3>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPinIcon className="w-4 h-4 mr-1" />
                        {property.city}, {property.province}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-primary-600">
                          {formatCurrency(property.price_min)}
                          {property.price_min !== property.price_max && (
                            <span className="text-sm font-normal text-gray-500">
                              - {formatCurrency(property.price_max)}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600">
                          <StarIcon className="w-4 h-4 text-yellow-400 mr-1" />
                          4.5
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          <div className="text-center mt-8 md:hidden">
            <Link to="/search" className="btn btn-outline">
              Lihat Semua Kost
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="section bg-white">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Mengapa Memilih KostKu?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Platform terpercaya dengan fitur lengkap untuk memudahkan pencarian kost
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Siap Menemukan Kost Impian Anda?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan pengguna yang telah menemukan kost terbaik melalui platform kami
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/search" className="btn btn-secondary btn-lg">
              Mulai Pencarian
            </Link>
            <Link to="/register" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary-600">
              Daftar Sekarang
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
